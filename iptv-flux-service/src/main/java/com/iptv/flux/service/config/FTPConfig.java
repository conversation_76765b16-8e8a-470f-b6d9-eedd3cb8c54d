package com.iptv.flux.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: FTPConfig
 * @author: Claude 4.0 sonnet
 * @description: FTP客户端配置类，管理FTP连接池和相关参数
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Configuration
@Slf4j
public class FTPConfig {

    /**
     * FTP连接配置属性
     */
    @Data
    @ConfigurationProperties(prefix = "traffic.sync.ftp")
    @Validated
    public static class FTPProperties {
        
        // 注意：FTP连接信息（host, port, username, password）现在从URL中动态解析
        // 这里只保留性能和行为相关的配置
        
        /**
         * 连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "连接超时时间不能少于1秒")
        private int connectionTimeout = 30000;
        
        /**
         * 数据传输超时时间（毫秒）
         */
        @Min(value = 1000, message = "数据传输超时时间不能少于1秒")
        private int dataTimeout = 60000;
        
        /**
         * 控制连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "控制连接超时时间不能少于1秒")
        private int controlTimeout = 30000;
        
        /**
         * 重试次数
         */
        @Min(value = 0, message = "重试次数不能为负数")
        @Max(value = 10, message = "重试次数不能超过10次")
        private int retryAttempts = 3;
        
        /**
         * 重试间隔时间（毫秒）
         */
        @Min(value = 100, message = "重试间隔不能少于100毫秒")
        private long retryInterval = 1000;
        
        /**
         * 是否使用被动模式
         */
        private boolean passiveMode = true;
        
        /**
         * 文件传输模式（二进制）
         */
        private boolean binaryMode = true;
        
        // 连接池配置已移除，现在使用动态连接方式
    }

    /**
     * FTP连接属性Bean
     */
    @Bean
    public FTPProperties ftpProperties() {
        return new FTPProperties();
    }

    // FTP连接池相关代码已移除，现在使用动态连接方式
}
