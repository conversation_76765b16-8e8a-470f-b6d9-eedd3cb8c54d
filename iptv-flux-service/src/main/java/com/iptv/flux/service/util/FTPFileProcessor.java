package com.iptv.flux.service.util;

import com.iptv.flux.service.config.FTPConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: FTPFileProcessor
 * @author: Claude 4.0 sonnet
 * @description: FTP文件处理器 - 负责FTP连接、文件下载、MD5校验等核心功能
 * @date: 2025/8/14 12:00
 * @version: 1.0
 */
@Component
@Slf4j
public class FTPFileProcessor {

    @Autowired
    private FTPConfig.FTPProperties ftpProperties;

    /**
     * 下载FTP文件到本地临时文件
     *
     * @param ftpUrl FTP文件URL，格式：ftp://username:password@host:port/path/file.txt
     * @return 下载的本地临时文件
     * @throws Exception 下载过程中的异常
     */
    public File downloadFile(String ftpUrl) throws Exception {
        log.info("流量平台-----> 开始下载FTP文件: {}", maskPassword(ftpUrl));
        long startTime = System.currentTimeMillis();

        FTPFileInfo fileInfo = parseFTPUrl(ftpUrl);
        FTPClient ftpClient = null;
        File tempFile = null;

        try {
            // 创建动态FTP连接
            ftpClient = createFTPClient(fileInfo);

            // 创建临时文件
            tempFile = createTempFile(fileInfo.getFileName());

            // 下载文件
            boolean success = downloadFileWithRetry(ftpClient, fileInfo.getRemotePath(), tempFile);

            if (!success) {
                throw new IOException("FTP文件下载失败: " + fileInfo.getRemotePath());
            }

            long duration = System.currentTimeMillis() - startTime;
            long fileSize = tempFile.length();
            log.info("流量平台-----> FTP文件下载成功，文件: {}，大小: {} bytes，耗时: {}ms",
                    fileInfo.getFileName(), fileSize, duration);

            return tempFile;

        } catch (Exception e) {
            // 下载失败时清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException ex) {
                    log.warn("清理临时文件失败: {}", tempFile.getAbsolutePath(), ex);
                }
            }
            log.error("流量平台-----> FTP文件下载失败: {}", maskPassword(ftpUrl), e);
            throw e;
        } finally {
            // 关闭FTP连接
            if (ftpClient != null) {
                closeFTPClient(ftpClient);
            }
        }
    }

    /**
     * 计算文件MD5校验值
     *
     * @param file 要计算MD5的文件
     * @return MD5校验值（32位小写字符串）
     * @throws Exception 计算过程中的异常
     */
    public String calculateMD5(File file) throws Exception {
        log.debug("流量平台-----> 开始计算文件MD5: {}", file.getName());
        long startTime = System.currentTimeMillis();

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = bis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }

            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            String md5 = sb.toString();
            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 文件MD5计算完成: {}，耗时: {}ms", md5, duration);

            return md5;

        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("流量平台-----> 计算文件MD5失败: {}", file.getName(), e);
            throw e;
        }
    }

    /**
     * 验证文件MD5校验值
     *
     * @param file        要验证的文件
     * @param expectedMD5 期望的MD5值
     * @return 校验是否通过
     */
    public boolean verifyMD5(File file, String expectedMD5) {
        if (expectedMD5 == null || expectedMD5.trim().isEmpty()) {
            log.warn("流量平台-----> 未提供MD5校验值，跳过校验");
            return true;
        }

        try {
            String actualMD5 = calculateMD5(file);
            boolean isValid = expectedMD5.equalsIgnoreCase(actualMD5);

            if (isValid) {
                log.info("流量平台-----> 文件MD5校验通过: {}", file.getName());
            } else {
                log.error("流量平台-----> 文件MD5校验失败，期望: {}，实际: {}", expectedMD5, actualMD5);
            }

            return isValid;

        } catch (Exception e) {
            log.error("流量平台-----> MD5校验过程中发生异常", e);
            return false;
        }
    }

    /**
     * 清理临时文件
     *
     * @param file 要清理的文件
     */
    public void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("流量平台-----> 临时文件清理成功: {}", file.getName());
            } catch (IOException e) {
                log.warn("流量平台-----> 临时文件清理失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 创建动态FTP连接
     */
    private FTPClient createFTPClient(FTPFileInfo fileInfo) throws Exception {
        FTPClient ftpClient = new FTPClient();

        try {
            // 设置超时时间
            ftpClient.setConnectTimeout(ftpProperties.getConnectionTimeout());
            ftpClient.setDataTimeout(java.time.Duration.ofMillis(ftpProperties.getDataTimeout()));
            ftpClient.setControlKeepAliveTimeout(java.time.Duration.ofMillis(ftpProperties.getControlTimeout()));

            // 连接FTP服务器
            log.debug("流量平台-----> 连接FTP服务器: {}:{}", fileInfo.getHost(), fileInfo.getPort());
            ftpClient.connect(fileInfo.getHost(), fileInfo.getPort());

            // 登录
            boolean loginSuccess;
            if (fileInfo.getUsername() != null && fileInfo.getPassword() != null) {
                loginSuccess = ftpClient.login(fileInfo.getUsername(), fileInfo.getPassword());
            } else {
                // 匿名登录
                loginSuccess = ftpClient.login("anonymous", "");
            }

            if (!loginSuccess) {
                throw new IOException("FTP登录失败，用户名或密码错误");
            }

            // 设置传输模式
            if (ftpProperties.isBinaryMode()) {
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            }

            // 设置被动模式
            if (ftpProperties.isPassiveMode()) {
                ftpClient.enterLocalPassiveMode();
            }

            log.debug("流量平台-----> FTP连接创建成功，服务器: {}:{}",
                     fileInfo.getHost(), fileInfo.getPort());
            return ftpClient;

        } catch (Exception e) {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ex) {
                    log.warn("关闭FTP连接时发生异常", ex);
                }
            }
            throw e;
        }
    }

    /**
     * 关闭FTP连接
     */
    private void closeFTPClient(FTPClient ftpClient) {
        if (ftpClient != null && ftpClient.isConnected()) {
            try {
                ftpClient.logout();
                ftpClient.disconnect();
                log.debug("流量平台-----> FTP连接关闭成功");
            } catch (IOException e) {
                log.warn("流量平台-----> 关闭FTP连接时发生异常", e);
            }
        }
    }

    /**
     * 带重试的文件下载
     */
    private boolean downloadFileWithRetry(FTPClient ftpClient, String remotePath, File localFile) throws Exception {
        int attempts = 0;
        int maxAttempts = ftpProperties.getRetryAttempts() + 1;

        while (attempts < maxAttempts) {
            attempts++;
            try {
                log.debug("流量平台-----> 尝试下载文件，第 {} 次，远程路径: {}", attempts, remotePath);

                try (FileOutputStream fos = new FileOutputStream(localFile);
                     BufferedOutputStream bos = new BufferedOutputStream(fos)) {

                    boolean success = ftpClient.retrieveFile(remotePath, bos);
                    bos.flush();

                    if (success) {
                        log.debug("流量平台-----> 文件下载成功，第 {} 次尝试", attempts);
                        return true;
                    } else {
                        log.warn("流量平台-----> 文件下载失败，第 {} 次尝试，FTP响应: {}",
                                attempts, ftpClient.getReplyString());
                    }
                }

            } catch (IOException e) {
                log.warn("流量平台-----> 文件下载异常，第 {} 次尝试", attempts, e);

                if (attempts >= maxAttempts) {
                    throw e;
                }

                // 重试前等待
                try {
                    TimeUnit.MILLISECONDS.sleep(ftpProperties.getRetryInterval());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("下载过程被中断", ie);
                }
            }
        }

        return false;
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(String fileName) throws IOException {
        String prefix = "ftp_download_";
        String suffix = getFileExtension(fileName);

        Path tempFile = Files.createTempFile(prefix, suffix);
        File file = tempFile.toFile();
        file.deleteOnExit(); // JVM退出时自动删除

        log.debug("流量平台-----> 创建临时文件: {}", file.getAbsolutePath());
        return file;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ".tmp";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }

        return ".tmp";
    }

    /**
     * 解析FTP URL - 提取完整的连接信息
     * 支持格式：ftp://username:password@host:port/path/file.txt
     */
    private FTPFileInfo parseFTPUrl(String ftpUrl) throws Exception {
        try {
            if (ftpUrl == null || ftpUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("FTP URL不能为空");
            }

            URI uri = new URI(ftpUrl);

            // 验证协议
            if (!"ftp".equalsIgnoreCase(uri.getScheme())) {
                throw new IllegalArgumentException("URL协议必须是FTP");
            }

            // 解析主机和端口
            String host = uri.getHost();
            if (host == null || host.trim().isEmpty()) {
                throw new IllegalArgumentException("FTP URL中缺少主机地址");
            }

            int port = uri.getPort();
            if (port == -1) {
                port = 21; // FTP默认端口
            }

            // 解析用户信息
            String userInfo = uri.getUserInfo();
            String username = null;
            String password = null;

            if (userInfo != null && !userInfo.trim().isEmpty()) {
                String[] userParts = userInfo.split(":", 2);
                username = userParts[0];
                if (userParts.length > 1) {
                    password = userParts[1];
                }
            }

            // 解析路径和文件名
            String path = uri.getPath();
            if (path == null || path.trim().isEmpty() || "/".equals(path)) {
                throw new IllegalArgumentException("FTP URL中缺少文件路径");
            }

            String fileName = path.substring(path.lastIndexOf('/') + 1);
            if (fileName.isEmpty()) {
                throw new IllegalArgumentException("FTP URL中缺少文件名");
            }

            log.debug("流量平台-----> FTP URL解析成功 - 主机: {}:{}, 用户: {}, 文件: {}",
                     host, port, username, fileName);

            return new FTPFileInfo(host, port, username, password, path, fileName);

        } catch (Exception e) {
            log.error("流量平台-----> 解析FTP URL失败: {}", maskPassword(ftpUrl), e);
            throw new IllegalArgumentException("无效的FTP URL格式: " + e.getMessage(), e);
        }
    }

    /**
     * 屏蔽URL中的密码信息
     */
    private String maskPassword(String ftpUrl) {
        if (ftpUrl == null) {
            return null;
        }
        return ftpUrl.replaceAll("://[^:]+:[^@]+@", "://***:***@");
    }

    /**
     * FTP文件信息 - 包含完整的连接和文件信息
     */
    private static class FTPFileInfo {
        private final String host;
        private final int port;
        private final String username;
        private final String password;
        private final String remotePath;
        private final String fileName;

        public FTPFileInfo(String host, int port, String username, String password,
                          String remotePath, String fileName) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
            this.remotePath = remotePath;
            this.fileName = fileName;
        }

        public String getHost() {
            return host;
        }

        public int getPort() {
            return port;
        }

        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }

        public String getRemotePath() {
            return remotePath;
        }

        public String getFileName() {
            return fileName;
        }
    }
}
